import React, { useState, useEffect, useRef } from 'react';
import { TrendingUp, Wallet, Activity } from 'lucide-react';
import RecentPnLTable from './RecentPnLTable';
import HoldingsTable from './HoldingsTable';
import ActivityTable from './ActivityTable';

interface WalletDetailTabsProps {
  walletAddress: string;
}

type TabId = 'recent-pnl' | 'holdings' | 'activity';

interface TabConfig {
  id: TabId;
  label: string;
  icon: React.ReactNode;
  component: React.ComponentType<{ walletAddress: string }>;
}

const WalletDetailTabs: React.FC<WalletDetailTabsProps> = ({ walletAddress }) => {
  const [activeTab, setActiveTab] = useState<TabId>('recent-pnl');
  const [tableHeight, setTableHeight] = useState<number>(600);
  const containerRef = useRef<HTMLDivElement>(null);

  const tabs: TabConfig[] = [
    {
      id: 'recent-pnl',
      label: 'Recent PnL',
      icon: <TrendingUp className="w-4 h-4" />,
      component: RecentPnLTable
    },
    {
      id: 'holdings',
      label: 'Holdings',
      icon: <Wallet className="w-4 h-4" />,
      component: HoldingsTable
    },
    {
      id: 'activity',
      label: 'Activity',
      icon: <Activity className="w-4 h-4" />,
      component: ActivityTable
    }
  ];

  // Calculate dynamic height based on available viewport space
  const calculateTableHeight = () => {
    if (!containerRef.current) return 600;

    const viewportHeight = window.innerHeight;
    const containerTop = containerRef.current.getBoundingClientRect().top;
    const padding = 120; // Account for navbar, padding, and footer space
    
    return Math.max(400, viewportHeight - containerTop - padding);
  };

  useEffect(() => {
    const updateHeight = () => {
      const newHeight = calculateTableHeight();
      setTableHeight(newHeight);
    };

    // Initial calculation
    updateHeight();

    // Update on resize
    window.addEventListener('resize', updateHeight);
    
    // Update after a short delay to account for layout changes
    const timeoutId = setTimeout(updateHeight, 100);

    return () => {
      window.removeEventListener('resize', updateHeight);
      clearTimeout(timeoutId);
    };
  }, [activeTab]);

  const handleTabChange = (tabId: TabId) => {
    setActiveTab(tabId);
  };

  const renderTabContent = () => {
    const activeTabConfig = tabs.find(tab => tab.id === activeTab);
    if (!activeTabConfig) return null;

    const Component = activeTabConfig.component;
    return <Component walletAddress={walletAddress} />;
  };

  return (
    <div ref={containerRef} className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl border border-gray-800/50 overflow-hidden h-full flex flex-col">
      {/* Tab Navigation Header */}
      <div className="border-b border-gray-800/50">
        <div className="flex items-center justify-between px-6 py-4">
          {/* Tab Buttons */}
          <nav className="flex space-x-6" role="tablist">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`
                  relative flex items-center space-x-2 text-sm font-medium px-3 py-2 rounded-lg
                  transition-all duration-200 ease-in-out
                  ${
                    activeTab === tab.id
                      ? 'text-white bg-[#7FFFD4]/10 border border-[#7FFFD4]/30'
                      : 'text-gray-400 hover:text-gray-200 hover:bg-gray-800/30'
                  }
                `}
                role="tab"
                aria-selected={activeTab === tab.id}
                aria-controls={`${tab.id}-panel`}
              >
                <span className={`transition-colors duration-200 ${
                  activeTab === tab.id ? 'text-[#7FFFD4]' : ''
                }`}>
                  {tab.icon}
                </span>
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>

          {/* Tab Info */}
          <div className="text-gray-400 text-sm">
            {activeTab === 'recent-pnl' && 'Recent profit and loss data'}
            {activeTab === 'holdings' && 'Current token holdings'}
            {activeTab === 'activity' && 'Trading activity history'}
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div 
        className="flex-1 overflow-hidden"
        style={{ height: `${tableHeight}px` }}
      >
        <div
          id={`${activeTab}-panel`}
          role="tabpanel"
          aria-labelledby={`${activeTab}-tab`}
          className="h-full"
        >
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default WalletDetailTabs;
